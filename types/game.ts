export interface Article {
  id: string;
  title: string;
  summary: string; // Your 220-240 word content
  wordsToFind: string[];
  difficulty: 'easy' | 'medium' | 'hard';
  completed: boolean;
}

export interface Topic {
  id: string;
  subjectId: string;
  title: string;
  description: string;
  articles: Article[];
  completedArticles: number;
}

export interface Subject {
  id: string;
  title: string;
  description: string;
  color: string;
  icon: string;
  totalTopics: number;
  completedTopics: number;
}

export interface WordSearchGrid {
  grid: string[][];
  size: number;
  words: PlacedWord[];
}

export interface PlacedWord {
  word: string;
  startRow: number;
  startCol: number;
  direction: 'horizontal' | 'vertical' | 'diagonal';
  found: boolean;
}

export interface GameState {
  currentTopic: Topic | null;
  grid: WordSearchGrid | null;
  selectedCells: { row: number; col: number }[];
  foundWords: string[];
  gameStartTime: number | null;
  gameEndTime: number | null;
}

export interface UserProgress {
  completedTopics: string[];
  totalScore: number;
  achievements: string[];
  statistics: {
    totalGamesPlayed: number;
    averageTime: number;
    bestTime: number;
  };
}