import { View, Text, StyleSheet } from 'react-native';
import { useLocalSearchParams, Stack } from 'expo-router';
import { TopicList } from '@/components/TopicList';
import { subjects } from '@/data/subjects';

export default function SubjectTopicsScreen() {
  const { subjectId } = useLocalSearchParams<{ subjectId: string }>();
  
  const subject = subjects.find(s => s.id === subjectId);
  
  if (!subject) {
    return (
      <View style={styles.container}>
        <Text>Subject not found</Text>
      </View>
    );
  }

  return (
    <>
      <Stack.Screen 
        options={{ 
          title: subject.title,
          headerShown: true,
          headerBackTitle: 'Subjects'
        }} 
      />
      <View style={styles.container}>
        <Text style={styles.description}>{subject.description}</Text>
        <TopicList subjectId={subjectId} />
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8FAFC',
    paddingHorizontal: 20,
    paddingTop: 20,
  },
  description: {
    fontSize: 16,
    color: '#6B7280',
    marginBottom: 20,
    lineHeight: 24,
  },
});