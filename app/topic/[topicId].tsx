import { useLocalSearchParams } from 'expo-router';
import { View, Text, StyleSheet } from 'react-native';

export default function TopicSummary() {
    //Step 1: Get the topic ID from the URL - topicId will be whatever was passed in the URL
    const { topicId } = userLocalSearchParams(); 

    //Step 2: Find the topic data
    const topic = topics.find(t => t.id === topicId);

    //Step 3: Display the content
    return (
        <View style={styles.container}>
            <Text style={styles.title}>{topic?.title}</Text>
            <Text style={styles.content}>
                {"TEXT GOES HERE"}
                {topic?.summary}
            </Text>
        </View>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        padding: 16,
    },
    title: {
        fontSize: 24, 
        fontWeight: 'bold',
        marginBottom: 16,
    },
    content: {
        fontSize: 16,
        lineHeight: 24,
    },
});
        


