import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { router } from 'expo-router';
import { Subject } from '@/types/game';
import { BookO<PERSON>, Users, CircleCheck as CheckCircle } from 'lucide-react-native';

interface SubjectCardProps {
  subject: Subject;
}

export function SubjectCard({ subject }: SubjectCardProps) {
  const handlePress = () => {
    router.push(`/subjects/${subject.id}`);
  };

  const progressPercentage = subject.totalTopics > 0 
    ? Math.round((subject.completedTopics / subject.totalTopics) * 100) 
    : 0;

  return (
    <TouchableOpacity style={[styles.card, { borderLeftColor: subject.color }]} onPress={handlePress}>
      <View style={styles.header}>
        <View style={[styles.iconContainer, { backgroundColor: `${subject.color}20` }]}>
          <BookOpen size={24} color={subject.color} />
        </View>
        <View style={styles.headerText}>
          <Text style={styles.title}>{subject.title}</Text>
          <Text style={styles.topicCount}>{subject.totalTopics} topics</Text>
        </View>
        {subject.completedTopics > 0 && (
          <View style={styles.progressBadge}>
            <CheckCircle size={16} color="#10B981" />
            <Text style={styles.progressText}>{progressPercentage}%</Text>
          </View>
        )}
      </View>
      
      <Text style={styles.description} numberOfLines={3}>
        {subject.description}
      </Text>
      
      <View style={styles.footer}>
        <View style={styles.progressContainer}>
          <View style={styles.progressBar}>
            <View 
              style={[
                styles.progressFill, 
                { 
                  width: `${progressPercentage}%`,
                  backgroundColor: subject.color 
                }
              ]} 
            />
          </View>
          <Text style={styles.progressLabel}>
            {subject.completedTopics} of {subject.totalTopics} completed
          </Text>
        </View>
      </View>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  card: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    borderLeftWidth: 4,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  headerText: {
    flex: 1,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 2,
  },
  topicCount: {
    fontSize: 14,
    color: '#6B7280',
  },
  progressBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#ECFDF5',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  progressText: {
    fontSize: 12,
    color: '#065F46',
    fontWeight: '600',
    marginLeft: 4,
  },
  description: {
    fontSize: 14,
    color: '#6B7280',
    lineHeight: 20,
    marginBottom: 16,
  },
  footer: {
    marginTop: 'auto',
  },
  progressContainer: {
    marginBottom: 4,
  },
  progressBar: {
    height: 6,
    backgroundColor: '#F3F4F6',
    borderRadius: 3,
    marginBottom: 6,
  },
  progressFill: {
    height: '100%',
    borderRadius: 3,
  },
  progressLabel: {
    fontSize: 12,
    color: '#9CA3AF',
  },
});