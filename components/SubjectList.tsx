import { View, Text, StyleSheet, FlatList } from 'react-native';
import { Subject } from '@/types/game';
import { SubjectCard } from './SubjectCard';
import { subjects } from '@/data/subjects';

export function SubjectList() {
  return (
    <View style={styles.container}>
      <Text style={styles.subtitle}>
        Choose a subject to explore educational topics and word search puzzles
      </Text>
      <FlatList
        data={subjects}
        renderItem={({ item }) => <SubjectCard subject={item} />}
        keyExtractor={(item) => item.id}
        showsVerticalScrollIndicator={false}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  subtitle: {
    fontSize: 16,
    color: '#6B7280',
    marginBottom: 20,
  },
});