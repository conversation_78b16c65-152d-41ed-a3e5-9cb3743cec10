import { View, Text, StyleSheet, FlatList } from 'react-native';
import { Topic } from '@/types/game';
import { TopicCard } from './TopicCard';
import { topics } from '@/data/topics';

interface TopicListProps {
  subjectId: string;
}

export function TopicList({ subjectId }: TopicListProps) {
  const subjectTopics = topics.filter(topic => topic.subjectId === subjectId);
  
  // TODO: Replace with actual game state management
  const getGameStatus = (topicId: string) => {
    // Mock data - replace with actual game state
    return {
      hasInProgressGame: topicId === '1', // Mock: first topic has in-progress game
      lastPlayedAt: topicId === '1' ? new Date() : undefined,
    };
  };

  return (
    <View style={styles.container}>
      {subjectTopics.length === 0 ? (
        <Text style={styles.emptyText}>No topics available for this subject yet.</Text>
      ) : (
        <>
          <Text style={styles.subtitle}>
            {subjectTopics.length} topics • Each includes educational content + word search puzzle
          </Text>
          <FlatList
            data={subjectTopics}
            renderItem={({ item }) => {
              const gameStatus = getGameStatus(item.id);
              return (
                <TopicCard 
                  topic={item} 
                  hasInProgressGame={gameStatus.hasInProgressGame}
                  lastPlayedAt={gameStatus.lastPlayedAt}
                />
              );
            }}
            keyExtractor={(item) => item.id}
            showsVerticalScrollIndicator={false}
          />
        </>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  subtitle: {
    fontSize: 16,
    color: '#6B7280',
    marginBottom: 20,
  },
  emptyText: {
    fontSize: 16,
    color: '#9CA3AF',
    textAlign: 'center',
    marginTop: 40,
  },
});