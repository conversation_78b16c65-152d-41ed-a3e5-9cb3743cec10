import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { Play, BookOpen, RotateCcw } from 'lucide-react-native';
import { router } from 'expo-router';
import { Topic } from '@/types/game';

interface TopicCardProps {
  topic: Topic;
  hasInProgressGame?: boolean;
  lastPlayedAt?: Date;
}

export function TopicCard({ topic, hasInProgressGame, lastPlayedAt }: TopicCardProps) {
  const handlePlayPress = () => {
    router.push(`/game/${topic.id}`);
  };

  const handleTopicPress = () => {
    // TODO: Navigate to topic detail/reading view
    router.push(`/summary/${topic.id}`);

    // For now, just log the topic title
    console.log('View topic details:', topic.title);
  };

  return (
    <View style={styles.card}>
      <View style={styles.header}>
        <TouchableOpacity onPress={handleTopicPress} style={styles.titleContainer}>
          <Text style={styles.title}>{topic.title}</Text>
        </TouchableOpacity>
        <Text style={styles.difficulty}>
          {topic.difficulty.toUpperCase()}
        </Text>
      </View>
      <TouchableOpacity onPress={handleTopicPress}>
        <Text style={styles.summary} numberOfLines={3}>
          {topic.summary}
        </Text>
      </TouchableOpacity>
      {hasInProgressGame && lastPlayedAt && (
        <View style={styles.gameStatus}>
          <RotateCcw size={14} color="#F59E0B" />
          <Text style={styles.gameStatusText}>
            In progress • {lastPlayedAt.toLocaleDateString()}
          </Text>
        </View>
      )}
      <View style={styles.features}>
        <View style={styles.feature}>
          <BookOpen size={16} color="#3B82F6" />
          <Text style={styles.featureText}>Educational Content</Text>
        </View>
        <View style={styles.feature}>
          <Play size={16} color="#10B981" />
          <Text style={styles.featureText}>Word Search Puzzle</Text>
        </View>
      </View>
      <View style={styles.footer}>
        <View style={styles.footerLeft}>
          <Text style={styles.difficulty}>
            Difficulty: {topic.difficulty}
          </Text>
          <Text style={styles.wordCount}>
            {topic.wordsToFind.length} words
          </Text>
        </View>
        <TouchableOpacity style={styles.playButton} onPress={handlePlayPress}>
          {hasInProgressGame ? (
            <RotateCcw size={18} color="#FFFFFF" />
          ) : (
            <Play size={18} color="#FFFFFF" />
          )}
          <Text style={styles.playButtonText}>
            {hasInProgressGame ? 'Resume' : 'Play'}
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  card: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  titleContainer: {
    flex: 1,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
  },
  category: {
    fontSize: 12,
    color: '#3B82F6',
    textTransform: 'uppercase',
    fontWeight: '500',
  },
  summary: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 12,
    lineHeight: 20,
  },
  gameStatus: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
    paddingHorizontal: 8,
    paddingVertical: 4,
    backgroundColor: '#FEF3C7',
    borderRadius: 6,
    alignSelf: 'flex-start',
  },
  gameStatusText: {
    fontSize: 12,
    color: '#92400E',
    marginLeft: 4,
    fontWeight: '500',
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  footerLeft: {
    flex: 1,
  },
  difficulty: {
    fontSize: 12,
    color: '#3B82F6',
    textTransform: 'uppercase',
    fontWeight: '500',
  },
  wordCount: {
    fontSize: 12,
    color: '#9CA3AF',
  },
  playButton: {
    backgroundColor: '#3B82F6',
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  playButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 6,
  },
  features: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
    paddingHorizontal: 4,
  },
  feature: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  featureText: {
    fontSize: 12,
    color: '#6B7280',
    marginLeft: 6,
    fontWeight: '500',
  },
});